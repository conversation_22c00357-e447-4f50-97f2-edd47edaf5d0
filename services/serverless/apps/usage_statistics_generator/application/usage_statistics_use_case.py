from datetime import datetime, timezone
from uuid import uuid4

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.domain.enums.usage_statistics import ReportTimeframe
from services.base.domain.repository.usage_stats_repository import UsageStatsRepository
from services.base.domain.schemas.usage_statistics_output import UsageStatistics
from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector_runner import (
    StatsCollectorRunner,
)
from services.serverless.apps.usage_statistics_generator.application.mappings import (
    get_timedelta_from_report_timeframe,
)
from services.serverless.apps.usage_statistics_generator.application.slack_bot.generate_cost_notification_report import (
    CostNotificationReporter,
)
from settings.app_config import settings


class UsageStatisticsUseCase:
    def __init__(
        self,
        collector_runner: StatsCollectorRunner,
        cost_notification_reporter: CostNotificationReporter,
        usage_stats_repository: UsageStatsRepository,
    ):
        self._collector_runner: StatsCollectorRunner = collector_runner
        self._cost_notification_reporter: CostNotificationReporter = cost_notification_reporter
        self._usage_stats_repository = usage_stats_repository

    async def execute_async(self, report_timeframe: ReportTimeframe) -> UsageStatistics:
        # Collect
        datetime_range_lte = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        datetime_range_gte = datetime_range_lte - get_timedelta_from_report_timeframe[report_timeframe]

        usage_statistics = await self._collector_runner.execute_async(
            time_gte=datetime_range_gte,
            time_lte=datetime_range_lte,
        )

        # Creates the result output structure
        result = UsageStatistics(
            id=uuid4(),
            usage_statistics_results=usage_statistics,
            report_timeframe=report_timeframe.value,
            aggregation_interval_start=datetime_range_gte,
            timestamp=datetime_range_lte,
        )

        # Inserts the output structure into the database
        usage_statistics = await self._usage_stats_repository.insert(
            usage_stats_documents=[result], force_strong_consistency=True
        )

        channel_id = settings.COST_NOTIFICATION_CHANNEL_ID
        if report_timeframe in [ReportTimeframe.MONTHLY, ReportTimeframe.QUARTERLY, ReportTimeframe.YEARLY]:
            await self._cost_notification_reporter.execute(
                usage_stats_document=usage_statistics[0], channel_id=channel_id
            )

        return result
