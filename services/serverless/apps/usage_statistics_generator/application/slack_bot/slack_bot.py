import logging
from io import By<PERSON><PERSON>
from typing import Optional

from slack_sdk.errors import SlackApiError
from slack_sdk.web.async_client import AsyncWebClient
from slack_sdk.web.async_slack_response import AsyncSlackResponse


class SlackBot:
    def __init__(self, client: AsyncWebClient):
        """
        Initializes the slack_bot with a given Slack bot client.

        Args:
            client (AsyncWebClient): The Slack bot client.
        """
        self._client = client

    async def post_message(self, message: str, channel_id: str) -> Optional[AsyncSlackResponse]:
        """
        Posts a message to a specified Slack channel.

        Args:
            message (str): The message to be posted.
            channel_id (str): The ID of the Slack channel.

        Returns:
            Optional[AsyncSlackResponse]: The response from <PERSON>lack if the message is posted successfully, otherwise None.

        Logs:
            SlackApiError: If there is an error sending the message, the error is logged.
            AssertionError: If the posted message text does not match the expected message, the error is logged.
        """
        try:
            response = await self._client.chat_postMessage(channel=channel_id, text=message)
            try:
                assert response
                assert response["message"]["text"] == message
                logging.info(f"message '{message}' posted successfully to channel {channel_id}.")
            except AssertionError:
                logging.error(f"posted message text does not match the expected message: {response['message']['text']}")
                return None

            return response
        except SlackApiError as e:
            logging.error(f"error sending message: {e.response['error']}")
            logging.exception("exception occurred")
            return None

    async def join_channel(self, channel_id: str) -> None:
        """
        Checks if the bot is a member of a specified Slack channel and joins the channel if not.

        Args:
            channel_id (str): The ID of the Slack channel.

        Logs:
            SlackApiError: If there is an error during the process, the error is logged.
        """
        try:
            auth_response = await self._client.auth_test()
            bot_user_id = auth_response["user_id"]

            # Check if bot is a member of the channel
            response = await self._client.conversations_members(channel=channel_id)
            if bot_user_id in response["members"]:
                logging.info(f"bot is already a member of the channel {channel_id}")
                return

            join_channel_response = await self._client.conversations_join(channel=channel_id)
            if join_channel_response["ok"]:
                logging.info(f"bot successfully joined the channel {channel_id}")
                return
            logging.error(f"failed to join the channel {channel_id}, response: {join_channel_response}")
        except SlackApiError as e:
            logging.error(f"error: {e.response['error']}")
            logging.exception("exception occurred")

    async def upload_csv_with_message(
        self, channel_id: str, csv_byte_array: BytesIO, message: str, filename: str
    ) -> Optional[AsyncSlackResponse]:
        """
        Uploads a .csv file to a specified Slack channel with an accompanying message.

        Args:
            channel_id (str): The ID of the Slack channel.
            csv_byte_array (bytes): The CSV content as a byte array.
            message (str): The message to accompany the file.
            filename (str): The name of the file uploaded.

        Returns:
            Optional[AsyncSlackResponse]: The response from Slack if the file is uploaded successfully, otherwise None.

        Logs:
            SlackApiError: If there is an error uploading the file, the error is logged.
        """
        try:
            response = await self._client.files_upload_v2(
                channel=channel_id,
                file=csv_byte_array,
                filename=filename,
                title="Uploaded CSV",
                initial_comment=message,
            )

            if response["ok"]:
                logging.info(f"file uploaded successfully to channel {channel_id}.")
                return response
            else:
                logging.error(f"failed to upload the file to channel {channel_id}.")
                return None
        except SlackApiError as e:
            logging.error(f"error uploading file: {e.response['error']}")
            logging.exception("exception occurred")
            return None

    async def get_last_messages(self, channel_id: str, count: int) -> list[dict]:
        """
        Retrieves the last `count` messages from a specified Slack channel.

        Args:
            channel_id (str): The ID of the Slack channel.
            count (int): The number of recent messages to retrieve.

        Returns:
            List[dict]: A list of messages from the channel. Each message is represented as a dictionary.

        Logs:
            SlackApiError: If there is an error retrieving the messages, the error is logged.
        """
        try:
            response = await self._client.conversations_history(channel=channel_id, limit=count)
            messages = response["messages"]
            logging.info(f"retrieved {len(messages)} messages from channel {channel_id}.")
            return messages
        except SlackApiError as e:
            logging.error(f"error retrieving messages: {e.response['error']}")
            logging.exception("exception occurred")
            return []

    async def delete_message(self, channel_id: str, message_ts: str) -> bool:
        """
        Deletes a message from a specified Slack channel.

        Args:
            channel_id (str): The ID of the Slack channel.
            message_ts (str): The timestamp of the message to be deleted.

        Logs:
            SlackApiError: If there is an error deleting the message, the error is logged.
        """
        try:
            response = await self._client.chat_delete(channel=channel_id, ts=message_ts)
            if response["ok"]:
                logging.info(f"message with ts '{message_ts}' deleted successfully from channel {channel_id}.")
                return True
            else:
                logging.error(f"failed to delete the message with ts '{message_ts}' from channel {channel_id}.")
        except SlackApiError as e:
            logging.error(f"error deleting message: {e.response['error']}")
            logging.exception("exception occurred")
        return False
