import logging
from io import Bytes<PERSON>

from slack_sdk.errors import SlackApiError
from slack_sdk.web.async_client import Async<PERSON>ebClient
from slack_sdk.web.async_slack_response import AsyncSlackResponse


class SlackBotError(Exception):
    """Base exception for SlackBot operations"""
    pass


class SlackMessagePostError(SlackBotError):
    """Raised when message posting fails"""
    pass


class SlackChannelJoinError(SlackBotError):
    """Raised when joining a channel fails"""
    pass


class SlackFileUploadError(SlackBotError):
    """Raised when file upload fails"""
    pass


class SlackMessageRetrievalError(SlackBotError):
    """Raised when message retrieval fails"""
    pass


class SlackMessageDeleteError(SlackBotError):
    """Raised when message deletion fails"""
    pass


class SlackBot:
    def __init__(self, client: AsyncWebClient):
        """
        Initializes the slack_bot with a given Slack bot client.

        Args:
            client (AsyncWebClient): The Slack bot client.
        """
        self._client = client

    async def post_message(self, message: str, channel_id: str) -> AsyncSlackResponse:
        """
        Posts a message to a specified Slack channel.

        Args:
            message (str): The message to be posted.
            channel_id (str): The ID of the Slack channel.

        Returns:
            AsyncSlackResponse: The response from Slack when the message is posted successfully.

        Raises:
            SlackMessagePostError: If the message fails to post or validation fails.
            SlackApiError: If there's an API-level error from Slack.
        """
        try:
            response = await self._client.chat_postMessage(channel=channel_id, text=message)
            if not response:
                raise SlackMessagePostError("Received None response from Slack API")

            # Validate response structure
            message_data = response.get("message")
            if not message_data:
                raise SlackMessagePostError("Response missing message data")

            posted_text = message_data.get("text")
            if posted_text != message:
                raise SlackMessagePostError(
                    f"Posted message text mismatch. Expected: '{message}', Got: '{posted_text}'"
                )

            logging.info(f"message '{message}' posted successfully to channel {channel_id}.")
            return response

        except SlackApiError:
            # Re-raise SlackApiError as-is for API-level issues
            raise
        except SlackMessagePostError:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Wrap other exceptions in our custom exception
            raise SlackMessagePostError(f"Failed to post message: {e}") from e

    async def join_channel(self, channel_id: str) -> None:
        """
        Checks if the bot is a member of a specified Slack channel and joins the channel if not.

        Args:
            channel_id (str): The ID of the Slack channel.

        Raises:
            SlackChannelJoinError: If joining the channel fails or validation fails.
            SlackApiError: If there's an API-level error from Slack.
        """
        try:
            auth_response = await self._client.auth_test()
            if not auth_response:
                raise SlackChannelJoinError("Received None response from auth_test")

            bot_user_id = auth_response.get("user_id")
            if not bot_user_id:
                raise SlackChannelJoinError("auth_test response does not contain user_id")

            # Check if bot is a member of the channel
            response = await self._client.conversations_members(channel=channel_id)
            if not response:
                raise SlackChannelJoinError("Received None response from conversations_members")

            members = response.get("members", [])
            if bot_user_id in members:
                logging.info(f"bot is already a member of the channel {channel_id}")
                return

            join_channel_response = await self._client.conversations_join(channel=channel_id)
            if not join_channel_response:
                raise SlackChannelJoinError("Received None response from conversations_join")

            if not join_channel_response.get("ok"):
                raise SlackChannelJoinError(
                    f"Failed to join channel {channel_id}, response: {join_channel_response}"
                )

            logging.info(f"bot successfully joined the channel {channel_id}")

        except SlackApiError:
            # Re-raise SlackApiError as-is for API-level issues
            raise
        except SlackChannelJoinError:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Wrap other exceptions in our custom exception
            raise SlackChannelJoinError(f"Failed to join channel: {e}") from e

    async def upload_csv_with_message(
        self, channel_id: str, csv_byte_array: BytesIO, message: str, filename: str
    ) -> AsyncSlackResponse:
        """
        Uploads a .csv file to a specified Slack channel with an accompanying message.

        Args:
            channel_id (str): The ID of the Slack channel.
            csv_byte_array (bytes): The CSV content as a byte array.
            message (str): The message to accompany the file.
            filename (str): The name of the file uploaded.

        Returns:
            AsyncSlackResponse: The response from Slack when the file is uploaded successfully.

        Raises:
            SlackFileUploadError: If the file upload fails or validation fails.
            SlackApiError: If there's an API-level error from Slack.
        """
        try:
            response = await self._client.files_upload_v2(
                channel=channel_id,
                file=csv_byte_array,
                filename=filename,
                title="Uploaded CSV",
                initial_comment=message,
            )

            if not response:
                raise SlackFileUploadError("Received None response from files_upload_v2")

            if not response.get("ok"):
                raise SlackFileUploadError(f"Failed to upload file to channel {channel_id}")

            logging.info(f"file uploaded successfully to channel {channel_id}.")
            return response

        except SlackApiError:
            # Re-raise SlackApiError as-is for API-level issues
            raise
        except SlackFileUploadError:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Wrap other exceptions in our custom exception
            raise SlackFileUploadError(f"Failed to upload file: {e}") from e

    async def get_last_messages(self, channel_id: str, count: int) -> list[dict]:
        """
        Retrieves the last `count` messages from a specified Slack channel.

        Args:
            channel_id (str): The ID of the Slack channel.
            count (int): The number of recent messages to retrieve.

        Returns:
            List[dict]: A list of messages from the channel. Each message is represented as a dictionary.

        Raises:
            SlackMessageRetrievalError: If message retrieval fails or validation fails.
            SlackApiError: If there's an API-level error from Slack.
        """
        try:
            response = await self._client.conversations_history(channel=channel_id, limit=count)
            if not response:
                raise SlackMessageRetrievalError("Received None response from conversations_history")

            messages = response.get("messages", [])
            logging.info(f"retrieved {len(messages)} messages from channel {channel_id}.")
            return messages

        except SlackApiError:
            # Re-raise SlackApiError as-is for API-level issues
            raise
        except SlackMessageRetrievalError:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Wrap other exceptions in our custom exception
            raise SlackMessageRetrievalError(f"Failed to retrieve messages: {e}") from e

    async def delete_message(self, channel_id: str, message_ts: str) -> None:
        """
        Deletes a message from a specified Slack channel.

        Args:
            channel_id (str): The ID of the Slack channel.
            message_ts (str): The timestamp of the message to be deleted.

        Raises:
            SlackMessageDeleteError: If message deletion fails or validation fails.
            SlackApiError: If there's an API-level error from Slack.
        """
        try:
            response = await self._client.chat_delete(channel=channel_id, ts=message_ts)
            if not response:
                raise SlackMessageDeleteError("Received None response from chat_delete")

            if not response.get("ok"):
                raise SlackMessageDeleteError(
                    f"Failed to delete message with ts '{message_ts}' from channel {channel_id}"
                )

            logging.info(f"message with ts '{message_ts}' deleted successfully from channel {channel_id}.")

        except SlackApiError:
            # Re-raise SlackApiError as-is for API-level issues
            raise
        except SlackMessageDeleteError:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Wrap other exceptions in our custom exception
            raise SlackMessageDeleteError(f"Failed to delete message: {e}") from e
